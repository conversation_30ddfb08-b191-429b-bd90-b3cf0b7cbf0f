#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
演示Excel保存格式
创建模拟数据来展示Excel保存格式与01_lottery_analysis.py的一致性
"""

import pandas as pd
import numpy as np
from datetime import datetime
from lottery_analysis_four_steps import LotteryAnalysisFourSteps

def create_demo_results():
    """
    创建演示用的命中结果
    """
    print("创建演示用的命中结果...")
    
    # 创建分析器实例
    analyzer = LotteryAnalysisFourSteps("lottery_data_all.xlsx")
    
    # 设置基本参数
    analyzer.lottery_type = "SSQ"
    analyzer.calculation_method = "集成预测"
    analyzer.start_row = 2000
    
    # 创建模拟的命中结果
    demo_results = []
    
    # 模拟5个命中结果
    for i in range(5):
        result = {
            'prediction_period': 20000 + i,
            'actual_period': 20001 + i,
            'method': '集成预测',
            'predicted_red': [1 + i, 5 + i, 10 + i, 15 + i, 20 + i, 25 + i],
            'predicted_blue': [8 + i % 3],
            'actual_red': [1 + i, 5 + i, 10 + i, 15 + i, 20 + i, 30 + i],
            'actual_blue': [8 + i % 3],
            'red_matches': 5,
            'blue_matches': 1,
            'total_matches': 6,
            'is_hit': True
        }
        demo_results.append(result)
    
    # 添加一个DLT的结果
    analyzer_dlt = LotteryAnalysisFourSteps("lottery_data_all.xlsx")
    analyzer_dlt.lottery_type = "DLT"
    analyzer_dlt.calculation_method = "贝叶斯预测"
    analyzer_dlt.start_row = 1500
    
    dlt_result = {
        'prediction_period': 24100,
        'actual_period': 24101,
        'method': '贝叶斯预测',
        'predicted_red': [2, 8, 15, 22, 30],
        'predicted_blue': [5, 11],
        'actual_red': [2, 8, 15, 22, 35],
        'actual_blue': [5, 11],
        'red_matches': 4,
        'blue_matches': 2,
        'total_matches': 6,
        'is_hit': True
    }
    
    return analyzer, demo_results, analyzer_dlt, [dlt_result]

def test_ssq_excel_format():
    """
    测试SSQ的Excel保存格式
    """
    print("\n测试SSQ的Excel保存格式")
    print("=" * 50)
    
    analyzer, demo_results, _, _ = create_demo_results()
    analyzer.results = demo_results
    
    print(f"创建了 {len(demo_results)} 个SSQ命中结果")
    
    # 保存结果
    if analyzer.step4_save_results():
        print("✓ SSQ Excel保存格式测试成功")
        return True
    else:
        print("✗ SSQ Excel保存格式测试失败")
        return False

def test_dlt_excel_format():
    """
    测试DLT的Excel保存格式
    """
    print("\n测试DLT的Excel保存格式")
    print("=" * 50)
    
    _, _, analyzer_dlt, dlt_results = create_demo_results()
    analyzer_dlt.results = dlt_results
    
    print(f"创建了 {len(dlt_results)} 个DLT命中结果")
    
    # 保存结果
    if analyzer_dlt.step4_save_results():
        print("✓ DLT Excel保存格式测试成功")
        return True
    else:
        print("✗ DLT Excel保存格式测试失败")
        return False

def show_excel_structure():
    """
    展示Excel文件结构
    """
    print("\n展示Excel文件结构")
    print("=" * 50)
    
    print("根据01_lottery_analysis.py的格式，Excel文件包含以下工作表：")
    print()
    
    print("1. 详细结果工作表")
    print("   列名：计算次序, 基于期号, 比对期号, 计算方法, 预测号码,")
    print("        实际号码, 红球命中数, 蓝球命中数, 总命中数, 是否命中, 6期内命中")
    print("   说明：包含所有计算结果的详细信息")
    print()
    
    print("2. 汇总统计工作表")
    print("   列名：统计项目, 数值")
    print("   内容：彩票类型, 计算方法, 开始行数, 总计算次数, 总命中次数,")
    print("        命中率(%), 6期内命中次数, 6期内命中率(%), 平均红球匹配数,")
    print("        平均蓝球匹配数, 平均总匹配数")
    print("   说明：包含统计汇总信息")
    print()
    
    print("3. 命中详情工作表")
    print("   列名：计算次序, 基于期号, 比对期号, 预测号码, 实际号码,")
    print("        命中详情, 6期内命中")
    print("   说明：只包含命中结果的详细信息")
    print()
    
    print("文件命名格式：lottery_analysis_results_{彩票类型}_{时间戳}.xlsx")
    print("例如：lottery_analysis_results_SSQ_20250607_181601.xlsx")

def compare_formats():
    """
    比较格式差异
    """
    print("\n格式对比")
    print("=" * 50)
    
    print("修改前的格式：")
    print("- 文件名：lottery_verification_results_{彩票类型}_{方法}_{时间戳}.xlsx")
    print("- 工作表：详细结果, 汇总统计")
    print("- 统计项目较少")
    print()
    
    print("修改后的格式（与01_lottery_analysis.py一致）：")
    print("- 文件名：lottery_analysis_results_{彩票类型}_{时间戳}.xlsx")
    print("- 工作表：详细结果, 汇总统计, 命中详情")
    print("- 统计项目完整，包含6期内命中统计")
    print("- 列名与参考文件完全一致")
    print()
    
    print("✓ 格式已完全对齐！")

def main():
    """
    主演示函数
    """
    print("Excel保存格式演示")
    print("=" * 50)
    print("本程序演示修改后的Excel保存格式与01_lottery_analysis.py的一致性")
    
    # 展示Excel结构
    show_excel_structure()
    
    # 测试SSQ格式
    if not test_ssq_excel_format():
        return
    
    # 测试DLT格式
    if not test_dlt_excel_format():
        return
    
    # 比较格式
    compare_formats()
    
    print("\n" + "=" * 50)
    print("Excel保存格式演示完成！")
    print("✓ 格式已与01_lottery_analysis.py完全一致")
    print("✓ 支持SSQ和DLT两种彩票类型")
    print("✓ 包含完整的统计信息和命中详情")
    print("=" * 50)

if __name__ == "__main__":
    main()
