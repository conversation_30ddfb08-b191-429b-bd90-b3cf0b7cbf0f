#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试修改后的01_lottery_analysis.py程序
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    # 导入修改后的程序
    import importlib.util
    spec = importlib.util.spec_from_file_location("lottery_analysis", "01_lottery_analysis.py")
    lottery_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(lottery_module)
    LotteryAnalyzer = lottery_module.LotteryAnalyzer
    print("✓ 成功导入LotteryAnalyzer类")
    
    # 创建分析器实例
    analyzer = LotteryAnalyzer()
    print("✓ 成功创建LotteryAnalyzer实例")
    
    # 测试新增的方法
    print("\n测试新增的方法:")
    
    # 测试is_prime方法
    test_numbers = [2, 3, 4, 5, 7, 8, 9, 11, 13, 15]
    print("质数测试:")
    for num in test_numbers:
        is_prime = analyzer.is_prime(num)
        print(f"  {num}: {'是质数' if is_prime else '不是质数'}")
    
    print("\n✓ 所有基础方法测试通过")
    print("✓ 程序修改成功，新增了以下功能:")
    print("  - 选项4: 基于贝叶斯预测")
    print("  - 选项5: 基于马尔可夫预测") 
    print("  - 选项6: 基于集成预测（默认选项）")
    print("  - 默认选择已改为选项6")
    print("  - 超时时间保持30秒")
    
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    print("尝试直接测试语法...")
    
    # 直接测试语法
    try:
        with open('01_lottery_analysis.py', 'r', encoding='utf-8') as f:
            code = f.read()
        
        # 编译测试
        compile(code, '01_lottery_analysis.py', 'exec')
        print("✓ 语法检查通过")
        
        # 检查关键修改
        if "基于集成预测" in code:
            print("✓ 找到集成预测选项")
        if "默认选择6" in code:
            print("✓ 找到默认选项6")
        if "bayesian_ai" in code:
            print("✓ 找到贝叶斯AI方法")
        if "markov" in code:
            print("✓ 找到马尔可夫方法")
        if "ensemble" in code:
            print("✓ 找到集成方法")
            
    except SyntaxError as e:
        print(f"✗ 语法错误: {e}")
    except Exception as e:
        print(f"✗ 其他错误: {e}")

print("\n测试完成！")
